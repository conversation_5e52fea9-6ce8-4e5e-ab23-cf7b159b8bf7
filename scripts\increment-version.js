#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const _filename = fileURLToPath(import.meta.url)
const _dirname = path.dirname(_filename)

/**
 * 时间格式化
 * @param {Date|string|number} date     任意可转成 Date 的值
 * @param {string} fmt                    格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string}                     格式化后的字符串
 *
 * 占位符说明：
 *  YYYY 年 | MM 月 | DD 日 | HH 时（24h）| hh 时（12h）
 *  mm 分  | ss 秒 | S 毫秒（3位）| A 上午/下午 | d 星期（0-6）
 *  dddd 星期中文 | ZZ 时区偏移（+0800）
 */
function formatDate(date = new Date(), fmt = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date);
  if (isNaN(d)) return 'Invalid Date';

  const pad = n => n.toString().padStart(2, '0');
  const week = ['日', '一', '二', '三', '四', '五', '六'];

  const map = {
    'Y+': d.getFullYear(),
    'M+': pad(d.getMonth() + 1),
    'D+': pad(d.getDate()),
    'H+': pad(d.getHours()),
    'h+': pad(d.getHours() % 12 || 12),
    'm+': pad(d.getMinutes()),
    's+': pad(d.getSeconds()),
    'S':  pad(d.getMilliseconds()).padStart(3, '0'),
    'A':  d.getHours() < 12 ? '上午' : '下午',
    'd+': d.getDay(),
    'dddd': `星期${week[d.getDay()]}`,
    'ZZ': (() => {
      const o = d.getTimezoneOffset();
      const h = pad(Math.floor(Math.abs(o) / 60));
      const m = pad(Math.abs(o) % 60);
      return (o <= 0 ? '+' : '-') + h + m;
    })()
  };

  for (const k in map) {
    const reg = new RegExp(k);
    if (reg.test(fmt)) {
      fmt = fmt.replace(reg, map[k]);
    }
  }
  return fmt;
}

function getGitShortHash() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return ''; // 不是 git 仓库或执行失败
  }
}

/**
 * 递增版本号的第三位数字
 * @param {string} version - 当前版本号，如 "1.0.0"
 * @returns {string} - 递增后的版本号，如 "1.0.1"
 */
function incrementPatchVersion(version) {
    const parts = version.split('.');
    if (parts.length >= 3) {
        parts[2] = (parseInt(parts[2]) + 1).toString();
    }
    let newVersion = parts.join('.');
    newVersion += `.${formatDate(new Date(), 'YYMMDDhhmmss')}.${getGitShortHash()}`
    return newVersion
}

/**
 * 更新package.json中的版本号
 * @param {string} newVersion - 新的版本号
 */
function updatePackageVersion(newVersion) {
    const pkgPath = path.resolve(_dirname, '../package.json');
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));
    const oldVersion = pkg.version;
    pkg.version = newVersion;
    fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2) + '\n');
    console.log(`✅ Version updated: ${oldVersion} → ${newVersion}`);
    return newVersion;
}

/**
 * 自动递增版本号并更新package.json
 */
function incrementAndUpdateVersion() {
    const pkgPath = path.resolve(_dirname, '../package.json');
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));
    const currentVersion = pkg.version;
    const newVersion = incrementPatchVersion(currentVersion);
    return newVersion
    
}

// 如果直接运行此脚本，则执行版本递增
if (import.meta.url === `file://${process.argv[1]}`) {
    try {
        const newVersion = incrementAndUpdateVersion();
        updatePackageVersion(newVersion);
        console.log(`🚀 Ready to build with version: ${newVersion}`);
        process.exit(0);
    } catch (error) {
        console.error('❌ Error incrementing version:', error);
        process.exit(1);
    }
}

export {
    incrementPatchVersion,
    updatePackageVersion,
    incrementAndUpdateVersion
}
