import { contextBridge, ipc<PERSON>enderer,app } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// 自定义 API 对象
const api = {
  getAppPath: () => ipcRenderer.invoke('get-app-path'),
  getAppDev: () => ipcRenderer.invoke('get-app-dev'),
  getConfig: () => ipcRenderer.invoke('get-config'),
  kuaijiejian: (params) => ipcRenderer.invoke('kuaijiejian', params),
  getMachineInfo: () => ipcRenderer.invoke('get-machine-info'),
  // getVersion: () => ipcRenderer.invoke('get-version'),
  // getVersionInfo: () => ipcRenderer.invoke('get-version-info')
}

// 使用上下文隔离时，将 API 暴露给渲染进程
console.log('zheli',process.contextIsolated)
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron',electronAPI )

    // 在现有的 contextBridge.exposeInMainWorld 中添加 saveFaceImage 方法
    contextBridge.exposeInMainWorld('api', api)
    contextBridge.exposeInMainWorld('myFs', require('fs'));
  } catch (error) {
    console.error(error)
  }
} else {

  // @ts-ignore (定义在 window 上)
  window.electron = electronAPI
  // @ts-ignore
  window.api = api
}
